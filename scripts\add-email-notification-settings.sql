-- Migration script to add email notification settings to business_settings table
-- Run this directly in Supabase SQL Editor

-- Add admin notification email setting (if not exists)
INSERT INTO business_settings (
    id,
    key,
    value,
    value_type,
    category,
    description,
    is_public,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'admin_notification_email',
    '<EMAIL>',
    'string',
    'notifications',
    'Email address for admin notifications (new reservations, payments, etc.)',
    false,
    now(),
    now()
) ON CONFLICT (key) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    updated_at = now();

-- Add global email notifications enabled setting
INSERT INTO business_settings (
    id,
    key,
    value,
    value_type,
    category,
    description,
    is_public,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'email_notifications_enabled',
    'true',
    'boolean',
    'notifications',
    'Enable/disable all email notifications globally',
    false,
    now(),
    now()
) ON CONFLICT (key) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    updated_at = now();

-- Add booking confirmation email enabled setting
INSERT INTO business_settings (
    id,
    key,
    value,
    value_type,
    category,
    description,
    is_public,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'booking_confirmation_email_enabled',
    'true',
    'boolean',
    'notifications',
    'Enable/disable booking confirmation emails to customers',
    false,
    now(),
    now()
) ON CONFLICT (key) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    updated_at = now();

-- Add admin email notifications enabled setting
INSERT INTO business_settings (
    id,
    key,
    value,
    value_type,
    category,
    description,
    is_public,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'admin_email_notifications_enabled',
    'true',
    'boolean',
    'notifications',
    'Enable/disable admin notification emails for new reservations and payments',
    false,
    now(),
    now()
) ON CONFLICT (key) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    updated_at = now();

-- Verify the settings were added
SELECT 
    key,
    value,
    value_type,
    category,
    description,
    is_public,
    created_at
FROM business_settings 
WHERE key IN (
    'admin_notification_email',
    'email_notifications_enabled',
    'booking_confirmation_email_enabled',
    'admin_email_notifications_enabled'
)
ORDER BY key;
