import { Resend } from "resend";
import {
	generateAdminNewReservationHTML,
	generateAdminNewReservationText,
	generateAdminPaymentReceivedHTML,
	generateAdminPaymentReceivedText,
} from "./email-templates/admin-templates";
import { emailConfig } from "./env";
import { getAdminNotificationEmail } from "./admin-notifications";

// Initialize Resend client
let resend: Resend | null = null;

// Rate limiting for Resend API (2 requests per second)
class EmailRateLimiter {
	private queue: Array<() => Promise<any>> = [];
	private isProcessing = false;
	private readonly RATE_LIMIT_DELAY = 500; // 500ms between emails (2 per second)

	async addToQueue<T>(emailFunction: () => Promise<T>): Promise<T> {
		return new Promise((resolve, reject) => {
			this.queue.push(async () => {
				try {
					const result = await emailFunction();
					resolve(result);
				} catch (error) {
					reject(error);
				}
			});

			if (!this.isProcessing) {
				this.processQueue();
			}
		});
	}

	private async processQueue() {
		if (this.isProcessing || this.queue.length === 0) {
			return;
		}

		this.isProcessing = true;

		while (this.queue.length > 0) {
			const emailFunction = this.queue.shift();
			if (emailFunction) {
				try {
					await emailFunction();
				} catch (error) {
					console.error("Error processing email from queue:", error);
				}

				// Wait before processing next email to respect rate limit
				if (this.queue.length > 0) {
					await new Promise((resolve) => setTimeout(resolve, this.RATE_LIMIT_DELAY));
				}
			}
		}

		this.isProcessing = false;
	}
}

// Global rate limiter instance
const emailRateLimiter = new EmailRateLimiter();

function getResendClient(): Resend {
	if (!resend && emailConfig.resend.apiKey) {
		resend = new Resend(emailConfig.resend.apiKey);
	}

	if (!resend) {
		throw new Error("Email service not configured. Please set RESEND_API_KEY environment variable.");
	}

	return resend;
}

export interface EmailData {
	to: string | string[];
	subject: string;
	html: string;
	text?: string;
	attachments?: Array<{
		filename: string;
		content: Buffer | Uint8Array;
		contentType?: string;
	}>;
}

/**
 * Send email using Resend with rate limiting
 */
export async function sendEmail(data: EmailData): Promise<{ success: boolean; messageId?: string; error?: string }> {
	return emailRateLimiter.addToQueue(async () => {
		try {
			console.log("=== EMAIL SERVICE DEBUG START ===");
			console.log("Email config check:");
			console.log("- API Key configured:", !!emailConfig.resend.apiKey);
			console.log("- From email:", emailConfig.resend.fromEmail);

			const client = getResendClient();

			const emailData: any = {
				from: emailConfig.resend.fromEmail,
				to: Array.isArray(data.to) ? data.to : [data.to],
				subject: data.subject,
				html: data.html,
				text: data.text,
				attachments: data.attachments,
			};

			console.log("Sending email with data:");
			console.log("- To:", emailData.to);
			console.log("- Subject:", emailData.subject);
			console.log("- Has HTML:", !!emailData.html);
			console.log("- Has text:", !!emailData.text);
			console.log("- Attachments count:", emailData.attachments?.length || 0);

			const result = await client.emails.send(emailData);

			console.log("Resend API result:", result);

			if (result.error) {
				console.error("Email sending error:", result.error);
				console.log("=== EMAIL SERVICE DEBUG END (ERROR) ===");
				return {
					success: false,
					error: result.error.message || "Failed to send email",
				};
			}

			console.log("Email sent successfully! Message ID:", result.data?.id);
			console.log("=== EMAIL SERVICE DEBUG END (SUCCESS) ===");
			return {
				success: true,
				messageId: result.data?.id,
			};
		} catch (error) {
			console.error("Email service error:", error);
			console.log("=== EMAIL SERVICE DEBUG END (EXCEPTION) ===");
			return {
				success: false,
				error: error instanceof Error ? error.message : "Unknown email error",
			};
		}
	});
}

/**
 * Send multiple emails with rate limiting
 * This function ensures emails are sent sequentially with proper delays
 */
export async function sendEmailBatch(
	emails: EmailData[]
): Promise<Array<{ success: boolean; messageId?: string; error?: string }>> {
	console.log(`=== BATCH EMAIL SENDING START (${emails.length} emails) ===`);

	const results: Array<{ success: boolean; messageId?: string; error?: string }> = [];

	for (let i = 0; i < emails.length; i++) {
		console.log(`Sending email ${i + 1}/${emails.length}: ${emails[i].subject}`);
		const result = await sendEmail(emails[i]);
		results.push(result);

		if (result.success) {
			console.log(`✅ Email ${i + 1} sent successfully`);
		} else {
			console.error(`❌ Email ${i + 1} failed:`, result.error);
		}
	}

	console.log(
		`=== BATCH EMAIL SENDING END (${results.filter((r) => r.success).length}/${emails.length} successful) ===`
	);
	return results;
}

/**
 * Send booking confirmation email
 */
export async function sendBookingConfirmationEmail(
	customerEmail: string,
	customerName: string,
	bookingData: {
		reservationNumber: string;
		serviceName: string;
		date: string;
		time: string;
		participants: number;
		totalAmount: number;
		specialRequests?: string;
		selectedOptions?: Array<{
			name: string;
			price: number;
			perParticipant?: boolean;
		}>;
		// Security deposit fields
		securityDepositRequired?: boolean;
		securityDepositAmount?: number;
		securityDepositStatus?: string;
	},
	pdfAttachment?: Buffer
): Promise<{ success: boolean; messageId?: string; error?: string }> {
	console.log("=== BOOKING CONFIRMATION EMAIL DEBUG START ===");
	console.log("Customer email:", customerEmail);
	console.log("Customer name:", customerName);
	console.log("Booking data:", bookingData);
	console.log("PDF attachment size:", pdfAttachment?.length || 0, "bytes");

	const subject = `Réservation confirmée !`;

	const html = generateBookingConfirmationHTML(customerName, bookingData);
	const text = generateBookingConfirmationText(customerName, bookingData);

	const attachments = pdfAttachment
		? [
				{
					filename: `confirmation-${bookingData.reservationNumber}.pdf`,
					content: pdfAttachment,
					contentType: "application/pdf",
				},
			]
		: undefined;

	console.log("Email subject:", subject);
	console.log("HTML length:", html.length);
	console.log("Text length:", text.length);
	console.log("Attachments:", attachments?.length || 0);

	const result = await sendEmail({
		to: customerEmail,
		subject,
		html,
		text,
		attachments,
	});

	console.log("Booking confirmation email result:", result);
	console.log("=== BOOKING CONFIRMATION EMAIL DEBUG END ===");

	return result;
}

/**
 * Send booking reminder email
 */
export async function sendBookingReminderEmail(
	customerEmail: string,
	customerName: string,
	bookingData: {
		reservationNumber: string;
		serviceName: string;
		date: string;
		time: string;
		participants: number;
		totalAmount: number;
	}
): Promise<{ success: boolean; messageId?: string; error?: string }> {
	const subject = `Rappel - Votre excursion demain - ${bookingData.reservationNumber}`;

	const html = generateBookingReminderHTML(customerName, bookingData);
	const text = generateBookingReminderText(customerName, bookingData);

	return sendEmail({
		to: customerEmail,
		subject,
		html,
		text,
	});
}

/**
 * Send payment confirmation email
 */
export async function sendPaymentConfirmationEmail(
	customerEmail: string,
	customerName: string,
	paymentData: {
		reservationNumber: string;
		serviceName: string;
		date: string;
		time: string;
		participants: number;
		amount: number;
		currency: string;
		paymentDate: string;
		isDeposit: boolean;
		remainingAmount?: number;
	},
	receiptAttachment?: Buffer
): Promise<{ success: boolean; messageId?: string; error?: string }> {
	const subject = paymentData.isDeposit ? `Acompte confirmé` : `Paiement confirmé`;

	const html = generatePaymentConfirmationHTML(customerName, paymentData);
	const text = generatePaymentConfirmationText(customerName, paymentData);

	const attachments = receiptAttachment
		? [
				{
					filename: `recu-${paymentData.reservationNumber}.pdf`,
					content: receiptAttachment,
					contentType: "application/pdf",
				},
			]
		: undefined;

	return sendEmail({
		to: customerEmail,
		subject,
		html,
		text,
		attachments,
	});
}

/**
 * Send cancellation notification email
 */
export async function sendCancellationEmail(
	customerEmail: string,
	customerName: string,
	cancellationData: {
		reservationNumber: string;
		serviceName: string;
		date: string;
		time: string;
		participants: number;
		totalAmount: number;
		refundAmount?: number;
		cancellationReason?: string;
	}
): Promise<{ success: boolean; messageId?: string; error?: string }> {
	const subject = `Annulation confirmée - ${cancellationData.reservationNumber}`;

	const html = generateCancellationHTML(customerName, cancellationData);
	const text = generateCancellationText(customerName, cancellationData);

	return sendEmail({
		to: customerEmail,
		subject,
		html,
		text,
	});
}

/**
 * Generate booking confirmation HTML email
 */
function generateBookingConfirmationHTML(customerName: string, bookingData: any): string {
	const isAdminCreated = bookingData.adminCreated === true;

	return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Confirmation de réservation</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .success-badge { background-color: #10b981; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .admin-notice { background-color: #e3f2fd; border: 1px solid #2196f3; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .admin-notice h4 { color: #1976d2; margin: 0 0 10px 0; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; text-align: center; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <p>Excursions éco-responsables en Guadeloupe</p>
      </div>

      <div class="success-badge">✓ Réservation Confirmée</div>

      ${
			isAdminCreated
				? `
      <div class="admin-notice">
        <h4>🛡️ Réservation créée par notre équipe</h4>
        <p>Cette réservation a été créée par notre équipe administrative pour vous faciliter le processus de réservation. Tous les détails ont été vérifiés et votre place est confirmée.</p>
      </div>
      `
				: ""
		}

      <h2>Merci ${customerName}, votre aventure guadeloupéenne vous attend !</h2>

      <div class="info-section">
        <h3>Détails de votre réservation :</h3>
        <p><strong>Numéro de réservation :</strong> ${bookingData.reservationNumber}</p>
        <p><strong>Service :</strong> ${bookingData.serviceName}</p>
        <p><strong>Date :</strong> ${bookingData.date}</p>
        <p><strong>Heure :</strong> ${bookingData.time}</p>
        <p><strong>Participants :</strong> ${bookingData.participants}</p>
        ${
			bookingData.selectedOptions && bookingData.selectedOptions.length > 0
				? `<div style="margin: 15px 0;">
            <strong>Options sélectionnées :</strong>
            <ul style="margin: 5px 0; padding-left: 20px;">
              ${bookingData.selectedOptions
					.map(
						(option: any) =>
							`<li>${option.name} - ${option.price}€${option.perParticipant ? " par participant" : ""}</li>`
					)
					.join("")}
            </ul>
          </div>`
				: ""
		}
        <p><strong>Montant total :</strong> ${bookingData.totalAmount}€</p>
        ${
			bookingData.securityDepositRequired
				? `<div style="margin: 15px 0; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px;">
            <h4 style="margin: 0 0 10px 0; color: #856404;">🛡️ Caution d'équipement</h4>
            <p style="margin: 0;"><strong>Montant de la caution :</strong> ${bookingData.securityDepositAmount}€</p>
            <p style="margin: 5px 0 0 0; font-size: 14px; color: #856404;">
              ${
					bookingData.securityDepositStatus === "authorized"
						? "✓ Caution autorisée sur votre carte bancaire. Elle sera libérée après retour de l'équipement en bon état."
						: bookingData.securityDepositStatus === "manual"
							? "Caution à régler à l'accueil lors de votre arrivée."
							: "Caution requise pour cette activité. Vous serez contacté pour les modalités de paiement."
				}
            </p>
          </div>`
				: ""
		}
        ${
			bookingData.specialRequests
				? `<p><strong>Demandes spéciales :</strong> ${bookingData.specialRequests}</p>`
				: ""
		}
      </div>

      <h3>Informations importantes :</h3>
      <ul>
        <li>Rendez-vous 15 minutes avant le départ au port de pêche de Petit-Canal</li>
        <li>Apportez : crème solaire, chapeau, maillot de bain et serviette</li>
        <li>Annulation gratuite jusqu'à 24h avant l'excursion</li>
        <li>En cas de mauvais temps, nous vous contacterons pour reporter</li>
        <li>Collations et équipements fournis selon l'excursion</li>
      </ul>

      <div class="info-section">
        <h3>Lieu de rendez-vous :</h3>
        <p><strong>Port de pêche de Petit-Canal</strong><br>
        Rue de la Darse, 97131 Petit-Canal, Guadeloupe</p>
        <p>📞 +33 6 40 24 44 25<br>
        📧 <EMAIL></p>
      </div>

      <div class="footer">
        <p>Merci pour votre confiance !</p>
        <p>Pour toute question, contactez-nous à <EMAIL></p>
        <p style="margin-top: 20px;">
          Soleil & Découverte - Excursions éco-responsables<br>
          Guadeloupe, France
        </p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate booking confirmation text email
 */
function generateBookingConfirmationText(customerName: string, bookingData: any): string {
	const isAdminCreated = bookingData.adminCreated === true;

	return `
Réservation confirmée !
Merci ${customerName}, votre aventure guadeloupéenne vous attend !

${
	isAdminCreated
		? `🛡️ RÉSERVATION CRÉÉE PAR NOTRE ÉQUIPE
Cette réservation a été créée par notre équipe administrative pour vous faciliter le processus de réservation. Tous les détails ont été vérifiés et votre place est confirmée.

`
		: ""
}Numéro de réservation : ${bookingData.reservationNumber}

Détails de votre réservation
${bookingData.serviceName}
${bookingData.date}
${bookingData.time}
${bookingData.participants} pers.
${
	bookingData.selectedOptions && bookingData.selectedOptions.length > 0
		? `
Options sélectionnées :
${bookingData.selectedOptions
	.map((option: any) => `- ${option.name} - ${option.price}€${option.perParticipant ? " par participant" : ""}`)
	.join("\n")}
`
		: ""
}
${bookingData.totalAmount}€
${
	bookingData.securityDepositRequired
		? `
🛡️ CAUTION D'ÉQUIPEMENT
Montant de la caution : ${bookingData.securityDepositAmount}€
${
	bookingData.securityDepositStatus === "authorized"
		? "✓ Caution autorisée sur votre carte bancaire. Elle sera libérée après retour de l'équipement en bon état."
		: bookingData.securityDepositStatus === "manual"
			? "Caution à régler à l'accueil lors de votre arrivée."
			: "Caution requise pour cette activité. Vous serez contacté pour les modalités de paiement."
}
`
		: ""
}
Informations importantes
• Rendez-vous 15 minutes avant le départ au port de pêche de Petit-Canal
• Apportez : crème solaire, chapeau, maillot de bain et serviette
• Annulation gratuite jusqu'à 24h avant l'excursion
• En cas de mauvais temps, nous vous contacterons pour reporter
• Collations et équipements fournis selon l'excursion

Lieu de rendez-vous
Port de pêche de Petit-Canal
Rue de la Darse, 97131 Petit-Canal, Guadeloupe

+33 6 40 24 44 25
<EMAIL>

Récapitulatif de commande
${bookingData.serviceName}
${bookingData.date} à ${bookingData.time}
${bookingData.participants} participants
${bookingData.totalAmount}€
Total
${bookingData.totalAmount}€

Vos informations
Nom : ${customerName}
Email : ${bookingData.customerEmail || ""}
Téléphone : ${bookingData.customerPhone || ""}
  `.trim();
}

/**
 * Generate booking reminder HTML email
 */
function generateBookingReminderHTML(customerName: string, bookingData: any): string {
	return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Rappel - Votre excursion demain</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .reminder-badge { background-color: #f59e0b; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; text-align: center; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <p>Excursions éco-responsables en Guadeloupe</p>
      </div>

      <div class="reminder-badge">⏰ Rappel - Excursion Demain</div>

      <h2>Bonjour ${customerName},</h2>
      
      <p>Votre excursion avec Soleil & Découverte a lieu demain ! Nous avons hâte de vous accueillir.</p>

      <div class="info-section">
        <h3>Rappel de votre réservation :</h3>
        <p><strong>Numéro de réservation :</strong> ${bookingData.reservationNumber}</p>
        <p><strong>Service :</strong> ${bookingData.serviceName}</p>
        <p><strong>Date :</strong> ${bookingData.date}</p>
        <p><strong>Heure :</strong> ${bookingData.time}</p>
        <p><strong>Participants :</strong> ${bookingData.participants}</p>
      </div>

      <h3>Informations importantes :</h3>
      <ul>
        <li>Rendez-vous 15 minutes avant l'heure prévue</li>
        <li>Apportez une pièce d'identité</li>
        <li>Prévoyez des vêtements adaptés à l'activité</li>
        <li>N'hésitez pas à nous contacter pour toute question</li>
      </ul>

      <div class="footer">
        <p>Nous avons hâte de vous accueillir !</p>
        <p>Pour toute question, contactez-nous à <EMAIL></p>
        <p style="margin-top: 20px;">
          Soleil & Découverte - Excursions éco-responsables<br>
          Guadeloupe, France
        </p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate booking reminder text email
 */
function generateBookingReminderText(customerName: string, bookingData: any): string {
	return `
SOLEIL & DÉCOUVERTE
Excursions éco-responsables en Guadeloupe

⏰ RAPPEL - EXCURSION DEMAIN

Bonjour ${customerName},

Votre excursion avec Soleil & Découverte a lieu demain ! Nous avons hâte de vous accueillir.

RAPPEL DE VOTRE RÉSERVATION :
- Numéro de réservation: ${bookingData.reservationNumber}
- Service: ${bookingData.serviceName}
- Date: ${bookingData.date}
- Heure: ${bookingData.time}
- Participants: ${bookingData.participants}

INFORMATIONS IMPORTANTES :
- Rendez-vous 15 minutes avant l'heure prévue
- Apportez une pièce d'identité
- Prévoyez des vêtements adaptés à l'activité
- N'hésitez pas à nous contacter pour toute question

Nous avons hâte de vous accueillir !
Pour toute question, contactez-nous à <EMAIL>

Soleil & Découverte - Excursions éco-responsables
Guadeloupe, France
  `.trim();
}

/**
 * Generate payment confirmation HTML email
 */
function generatePaymentConfirmationHTML(customerName: string, paymentData: any): string {
	const paymentTypeText = paymentData.isDeposit ? "Acompte" : "Paiement";
	const remainingText =
		paymentData.isDeposit && paymentData.remainingAmount
			? `<p><strong>⚠️ Solde restant à payer :</strong> ${paymentData.remainingAmount}€</p>`
			: "";

	return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${paymentTypeText} confirmé</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .success-badge { background-color: #10b981; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .payment-section { background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; text-align: center; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <p>Excursions éco-responsables en Guadeloupe</p>
      </div>

      <div class="success-badge">✓ ${paymentTypeText} Confirmé</div>

      <h2>Bonjour ${customerName},</h2>

      <p>Nous avons bien reçu votre ${paymentData.isDeposit ? "acompte" : "paiement"} pour votre réservation !</p>

      <div class="payment-section">
        <h3>Détails du paiement :</h3>
        <p><strong>Montant payé :</strong> ${paymentData.amount}€</p>
        <p><strong>Date de paiement :</strong> ${paymentData.paymentDate}</p>
        ${remainingText}
      </div>

      <div class="info-section">
        <h3>Détails de votre réservation :</h3>
        <p><strong>Numéro de réservation :</strong> ${paymentData.reservationNumber}</p>
        <p><strong>Service :</strong> ${paymentData.serviceName}</p>
        <p><strong>Date :</strong> ${paymentData.date}</p>
        <p><strong>Heure :</strong> ${paymentData.time}</p>
        <p><strong>Participants :</strong> ${paymentData.participants}</p>
      </div>

      <h3>Prochaines étapes :</h3>
      <ul>
        <li>Votre place est maintenant ${paymentData.isDeposit ? "réservée" : "confirmée"}</li>
        <li>Vous recevrez un rappel 24h avant votre excursion</li>
        ${paymentData.isDeposit ? "<li>Préparez le solde restant pour le jour J</li>" : ""}
        <li>Rendez-vous au point de départ 15 minutes avant l'heure prévue</li>
        <li>Apportez une pièce d'identité</li>
      </ul>

      <div class="footer">
        <p>Merci pour votre confiance !</p>
        <p>Pour toute question, contactez-nous à <EMAIL></p>
        <p style="margin-top: 20px;">
          Soleil & Découverte - Excursions éco-responsables<br>
          Guadeloupe, France
        </p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate payment confirmation text email
 */
function generatePaymentConfirmationText(customerName: string, paymentData: any): string {
	const paymentTypeText = paymentData.isDeposit ? "ACOMPTE" : "PAIEMENT";
	const remainingText =
		paymentData.isDeposit && paymentData.remainingAmount
			? `\n⚠️ SOLDE RESTANT À PAYER : ${paymentData.remainingAmount}€\nCe montant reste à régler avant votre excursion.`
			: "";

	return `
SOLEIL & DÉCOUVERTE
Excursions éco-responsables en Guadeloupe

✓ ${paymentTypeText} CONFIRMÉ

Bonjour ${customerName},

Nous avons bien reçu votre ${paymentData.isDeposit ? "acompte" : "paiement"} pour votre réservation !

DÉTAILS DU PAIEMENT :
- Montant payé : ${paymentData.amount}€
- Date de paiement : ${paymentData.paymentDate}${remainingText}

DÉTAILS DE VOTRE RÉSERVATION :
- Numéro de réservation : ${paymentData.reservationNumber}
- Service : ${paymentData.serviceName}
- Date : ${paymentData.date}
- Heure : ${paymentData.time}
- Participants : ${paymentData.participants}

PROCHAINES ÉTAPES :
- Votre place est maintenant ${paymentData.isDeposit ? "réservée" : "confirmée"}
- Vous recevrez un rappel 24h avant votre excursion${
		paymentData.isDeposit ? "\n- Préparez le solde restant pour le jour J" : ""
	}
- Rendez-vous au point de départ 15 minutes avant l'heure prévue
- Apportez une pièce d'identité

Merci pour votre confiance !
Pour toute question, contactez-nous à <EMAIL>

Soleil & Découverte - Excursions éco-responsables
Guadeloupe, France
  `.trim();
}

/**
 * Generate cancellation HTML email
 */
function generateCancellationHTML(customerName: string, cancellationData: any): string {
	const refundText = cancellationData.refundAmount
		? `<div class="refund-section">
        <h3>Remboursement :</h3>
        <p><strong>Montant remboursé :</strong> ${cancellationData.refundAmount}€</p>
        <p>Le remboursement sera traité dans les 5-7 jours ouvrables.</p>
      </div>`
		: "";

	const reasonText = cancellationData.cancellationReason
		? `<p><strong>Raison de l'annulation :</strong> ${cancellationData.cancellationReason}</p>`
		: "";

	return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Annulation confirmée</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .cancel-badge { background-color: #f59e0b; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .refund-section { background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; text-align: center; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <p>Excursions éco-responsables en Guadeloupe</p>
      </div>

      <div class="cancel-badge">⚠️ Annulation Confirmée</div>

      <h2>Bonjour ${customerName},</h2>

      <p>Nous confirmons l'annulation de votre réservation.</p>
      ${reasonText}

      <div class="info-section">
        <h3>Détails de la réservation annulée :</h3>
        <p><strong>Numéro de réservation :</strong> ${cancellationData.reservationNumber}</p>
        <p><strong>Service :</strong> ${cancellationData.serviceName}</p>
        <p><strong>Date :</strong> ${cancellationData.date}</p>
        <p><strong>Heure :</strong> ${cancellationData.time}</p>
        <p><strong>Participants :</strong> ${cancellationData.participants}</p>
        <p><strong>Montant total :</strong> ${cancellationData.totalAmount}€</p>
      </div>

      ${refundText}

      <p>Nous espérons avoir l'occasion de vous accueillir prochainement pour une autre excursion !</p>

      <div class="footer">
        <p>Pour toute question, contactez-nous à <EMAIL></p>
        <p style="margin-top: 20px;">
          Soleil & Découverte - Excursions éco-responsables<br>
          Guadeloupe, France
        </p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate cancellation text email
 */
function generateCancellationText(customerName: string, cancellationData: any): string {
	const refundText = cancellationData.refundAmount
		? `\nREMBOURSEMENT :\n- Montant remboursé : ${cancellationData.refundAmount}€\n- Le remboursement sera traité dans les 5-7 jours ouvrables.`
		: "";

	const reasonText = cancellationData.cancellationReason
		? `\nRaison de l'annulation : ${cancellationData.cancellationReason}`
		: "";

	return `
SOLEIL & DÉCOUVERTE
Excursions éco-responsables en Guadeloupe

⚠️ ANNULATION CONFIRMÉE

Bonjour ${customerName},

Nous confirmons l'annulation de votre réservation.${reasonText}

DÉTAILS DE LA RÉSERVATION ANNULÉE :
- Numéro de réservation : ${cancellationData.reservationNumber}
- Service : ${cancellationData.serviceName}
- Date : ${cancellationData.date}
- Heure : ${cancellationData.time}
- Participants : ${cancellationData.participants}
- Montant total : ${cancellationData.totalAmount}€${refundText}

Nous espérons avoir l'occasion de vous accueillir prochainement pour une autre excursion !

Pour toute question, contactez-nous à <EMAIL>

Soleil & Découverte - Excursions éco-responsables
Guadeloupe, France
  `.trim();
}

/**
 * Send admin notification email for new reservations (legacy function with explicit emails)
 */
export async function sendAdminNewReservationEmail(
	adminEmails: string[],
	reservationData: {
		reservationNumber: string;
		customerName: string;
		customerEmail: string;
		serviceName: string;
		date: string;
		time: string;
		participants: number;
		totalAmount: number;
		specialRequests?: string;
	}
): Promise<{ success: boolean; messageId?: string; error?: string }> {
	const subject = `Nouvelle Réservation`;

	const html = generateAdminNewReservationHTML(reservationData);
	const text = generateAdminNewReservationText(reservationData);

	return sendEmail({
		to: adminEmails,
		subject,
		html,
		text,
	});
}

/**
 * Send admin notification email for new reservations (uses configured admin email)
 */
export async function sendAdminNewReservationNotification(reservationData: {
	reservationNumber: string;
	customerName: string;
	customerEmail: string;
	serviceName: string;
	date: string;
	time: string;
	participants: number;
	totalAmount: number;
	specialRequests?: string;
	// Security deposit fields
	securityDepositRequired?: boolean;
	securityDepositAmount?: number;
	securityDepositStatus?: string;
}): Promise<{ success: boolean; messageId?: string; error?: string }> {
	try {
		const adminEmail = await getAdminNotificationEmail();
		console.log("Sending admin new reservation notification to:", adminEmail);

		return sendAdminNewReservationEmail([adminEmail], reservationData);
	} catch (error) {
		console.error("Error getting admin notification email:", error);
		// Fall back to default email
		return sendAdminNewReservationEmail(["<EMAIL>"], reservationData);
	}
}

/**
 * Send admin notification email for payments received (legacy function with explicit emails)
 */
export async function sendAdminPaymentReceivedEmail(
	adminEmails: string[],
	paymentData: {
		reservationNumber: string;
		customerName: string;
		amount: number;
		currency: string;
		paymentDate: string;
		isDeposit: boolean;
	}
): Promise<{ success: boolean; messageId?: string; error?: string }> {
	const subject = `${paymentData.isDeposit ? "Acompte" : "Paiement complet"} Reçu`;

	const html = generateAdminPaymentReceivedHTML(paymentData);
	const text = generateAdminPaymentReceivedText(paymentData);

	return sendEmail({
		to: adminEmails,
		subject,
		html,
		text,
	});
}

/**
 * Send admin notification email for payments received (uses configured admin email)
 */
export async function sendAdminPaymentReceivedNotification(paymentData: {
	reservationNumber: string;
	customerName: string;
	amount: number;
	currency: string;
	paymentDate: string;
	isDeposit: boolean;
}): Promise<{ success: boolean; messageId?: string; error?: string }> {
	try {
		const adminEmail = await getAdminNotificationEmail();
		console.log("Sending admin payment received notification to:", adminEmail);

		return sendAdminPaymentReceivedEmail([adminEmail], paymentData);
	} catch (error) {
		console.error("Error getting admin notification email:", error);
		// Fall back to default email
		return sendAdminPaymentReceivedEmail(["<EMAIL>"], paymentData);
	}
}

/**
 * Send invoice email
 */
export async function sendInvoiceEmail(
	customerEmail: string,
	customerName: string,
	invoiceData: {
		invoiceNumber: string;
		reservationNumber: string;
		serviceName: string;
		totalAmount: number;
		currency: string;
		paymentStatus: string;
	},
	invoiceAttachment: Buffer
): Promise<{ success: boolean; messageId?: string; error?: string }> {
	const subject = `Facture ${invoiceData.invoiceNumber} - ${invoiceData.reservationNumber}`;

	const html = generateInvoiceHTML(customerName, invoiceData);
	const text = generateInvoiceText(customerName, invoiceData);

	const attachments = [
		{
			filename: `facture-${invoiceData.invoiceNumber}.pdf`,
			content: invoiceAttachment,
			contentType: "application/pdf",
		},
	];

	return sendEmail({
		to: customerEmail,
		subject,
		html,
		text,
		attachments,
	});
}

/**
 * Generate invoice HTML email
 */
function generateInvoiceHTML(customerName: string, invoiceData: any): string {
	const statusText =
		invoiceData.paymentStatus === "paid"
			? "payée"
			: invoiceData.paymentStatus === "partial"
				? "partiellement payée"
				: "en attente de paiement";
	const statusColor =
		invoiceData.paymentStatus === "paid"
			? "#10b981"
			: invoiceData.paymentStatus === "partial"
				? "#f59e0b"
				: "#ef4444";

	return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Facture ${invoiceData.invoiceNumber}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .invoice-badge { background-color: #3b82f6; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .status-section { padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid ${statusColor}; background-color: ${statusColor}15; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; text-align: center; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <p>Excursions éco-responsables en Guadeloupe</p>
      </div>

      <div class="invoice-badge">📄 Facture ${invoiceData.invoiceNumber}</div>

      <h2>Bonjour ${customerName},</h2>

      <p>Veuillez trouver ci-joint votre facture pour votre réservation.</p>

      <div class="info-section">
        <h3>Détails de la facture :</h3>
        <p><strong>Numéro de facture :</strong> ${invoiceData.invoiceNumber}</p>
        <p><strong>Réservation :</strong> ${invoiceData.reservationNumber}</p>
        <p><strong>Service :</strong> ${invoiceData.serviceName}</p>
        <p><strong>Montant total :</strong> ${invoiceData.totalAmount} ${invoiceData.currency}</p>
      </div>

      <div class="status-section">
        <h3>Statut du paiement :</h3>
        <p style="color: ${statusColor}; font-weight: bold;">Facture ${statusText}</p>
      </div>

      <p>La facture PDF est jointe à cet email. Conservez-la pour vos dossiers.</p>

      <div class="footer">
        <p>Merci pour votre confiance !</p>
        <p>Pour toute question, contactez-nous à <EMAIL></p>
        <p style="margin-top: 20px;">
          Soleil & Découverte - Excursions éco-responsables<br>
          Guadeloupe, France
        </p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate invoice text email
 */
function generateInvoiceText(customerName: string, invoiceData: any): string {
	const statusText =
		invoiceData.paymentStatus === "paid"
			? "PAYÉE"
			: invoiceData.paymentStatus === "partial"
				? "PARTIELLEMENT PAYÉE"
				: "EN ATTENTE DE PAIEMENT";

	return `
SOLEIL & DÉCOUVERTE
Excursions éco-responsables en Guadeloupe

📄 FACTURE ${invoiceData.invoiceNumber}

Bonjour ${customerName},

Veuillez trouver ci-joint votre facture pour votre réservation.

DÉTAILS DE LA FACTURE :
- Numéro de facture : ${invoiceData.invoiceNumber}
- Réservation : ${invoiceData.reservationNumber}
- Service : ${invoiceData.serviceName}
- Montant total : ${invoiceData.totalAmount} ${invoiceData.currency}

STATUT DU PAIEMENT :
Facture ${statusText}

La facture PDF est jointe à cet email. Conservez-la pour vos dossiers.

Merci pour votre confiance !
Pour toute question, contactez-nous à <EMAIL>

Soleil & Découverte - Excursions éco-responsables
Guadeloupe, France
  `.trim();
}

/**
 * Send all payment-related emails in a batch with rate limiting
 * This function handles the common scenario of sending multiple emails after payment confirmation
 */
export async function sendPaymentEmailBatch(params: {
	// Customer emails
	customerEmail: string;
	customerName: string;
	bookingConfirmationData?: {
		reservationNumber: string;
		serviceName: string;
		date: string;
		time: string;
		participants: number;
		totalAmount: number;
		specialRequests?: string;
		selectedOptions?: Array<{
			name: string;
			price: number;
			perParticipant?: boolean;
		}>;
		// Security deposit fields
		securityDepositRequired?: boolean;
		securityDepositAmount?: number;
		securityDepositStatus?: string;
	};
	paymentConfirmationData?: {
		reservationNumber: string;
		serviceName: string;
		date: string;
		time: string;
		participants: number;
		amount: number;
		currency: string;
		paymentDate: string;
		isDeposit: boolean;
		remainingAmount?: number;
	};
	// Admin emails
	adminNewReservationData?: {
		reservationNumber: string;
		customerName: string;
		customerEmail: string;
		serviceName: string;
		date: string;
		time: string;
		participants: number;
		totalAmount: number;
		specialRequests?: string;
		// Security deposit fields
		securityDepositRequired?: boolean;
		securityDepositAmount?: number;
		securityDepositStatus?: string;
	};
	adminPaymentReceivedData?: {
		reservationNumber: string;
		customerName: string;
		amount: number;
		currency: string;
		paymentDate: string;
		isDeposit: boolean;
	};
	// Attachments
	bookingPdfAttachment?: Buffer;
	paymentReceiptAttachment?: Buffer;
}): Promise<{
	bookingConfirmation?: { success: boolean; messageId?: string; error?: string };
	paymentConfirmation?: { success: boolean; messageId?: string; error?: string };
	adminNewReservation?: { success: boolean; messageId?: string; error?: string };
	adminPaymentReceived?: { success: boolean; messageId?: string; error?: string };
}> {
	console.log("=== PAYMENT EMAIL BATCH START ===");

	const results: any = {};

	// Send booking confirmation email if data provided
	if (params.bookingConfirmationData) {
		console.log("Sending booking confirmation email...");
		results.bookingConfirmation = await sendBookingConfirmationEmail(
			params.customerEmail,
			params.customerName,
			params.bookingConfirmationData,
			params.bookingPdfAttachment
		);
	}

	// Send payment confirmation email if data provided
	if (params.paymentConfirmationData) {
		console.log("Sending payment confirmation email...");
		results.paymentConfirmation = await sendPaymentConfirmationEmail(
			params.customerEmail,
			params.customerName,
			params.paymentConfirmationData,
			params.paymentReceiptAttachment
		);
	}

	// Send admin new reservation notification if data provided
	if (params.adminNewReservationData) {
		console.log("Sending admin new reservation notification...");
		results.adminNewReservation = await sendAdminNewReservationNotification(params.adminNewReservationData);
	}

	// Send admin payment received notification if data provided
	if (params.adminPaymentReceivedData) {
		console.log("Sending admin payment received notification...");
		results.adminPaymentReceived = await sendAdminPaymentReceivedNotification(params.adminPaymentReceivedData);
	}

	console.log("=== PAYMENT EMAIL BATCH END ===");
	return results;
}
