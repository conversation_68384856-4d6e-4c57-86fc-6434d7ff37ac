/**
 * <PERSON><PERSON><PERSON> to add email notification settings via API
 * Run this in browser console while on the admin page, or use a REST client
 */

async function addEmailNotificationSettings() {
    try {
        console.log("Adding email notification settings via API...");
        
        const response = await fetch('/api/admin/add-notification-setting', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log("✅ Email notification settings added successfully!");
            console.log("Results:", result.results);
            
            // Refresh the page to see the new settings
            window.location.reload();
        } else {
            console.error("❌ Failed to add settings:", result.error);
        }
    } catch (error) {
        console.error("❌ Error:", error);
    }
}

// Run the function
addEmailNotificationSettings();

/**
 * Alternative: Manual API calls
 * You can also run these individual fetch calls:
 */

/*
// Add global email notifications setting
fetch('/api/admin/settings', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        key: 'email_notifications_enabled',
        value: true,
        value_type: 'boolean',
        category: 'notifications',
        description: 'Enable/disable all email notifications globally',
        is_public: false
    })
});

// Add booking confirmation email setting
fetch('/api/admin/settings', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        key: 'booking_confirmation_email_enabled',
        value: true,
        value_type: 'boolean',
        category: 'notifications',
        description: 'Enable/disable booking confirmation emails to customers',
        is_public: false
    })
});

// Add admin email notifications setting
fetch('/api/admin/settings', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        key: 'admin_email_notifications_enabled',
        value: true,
        value_type: 'boolean',
        category: 'notifications',
        description: 'Enable/disable admin notification emails',
        is_public: false
    })
});
*/
