#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to add email notification settings to business_settings table
 * Run with: npx tsx scripts/update-admin-notification-setting.ts
 */

import { supabase } from "../lib/supabase";

async function updateEmailNotificationSettings() {
	console.log("Adding email notification settings...");

	const settings = [
		{
			key: "admin_notification_email",
			value: "<EMAIL>",
			value_type: "string",
			category: "notifications",
			description: "Email address for admin notifications (new reservations, payments, etc.)",
			is_public: false,
		},
		{
			key: "email_notifications_enabled",
			value: "true",
			value_type: "boolean",
			category: "notifications",
			description: "Enable/disable all email notifications globally",
			is_public: false,
		},
		{
			key: "booking_confirmation_email_enabled",
			value: "true",
			value_type: "boolean",
			category: "notifications",
			description: "Enable/disable booking confirmation emails to customers",
			is_public: false,
		},
		{
			key: "admin_email_notifications_enabled",
			value: "true",
			value_type: "boolean",
			category: "notifications",
			description: "Enable/disable admin notification emails for new reservations and payments",
			is_public: false,
		},
	];

	try {
		for (const setting of settings) {
			console.log(`Adding/updating setting: ${setting.key}`);

			const { data, error } = await supabase
				.from("business_settings")
				.upsert(setting, { onConflict: "key" })
				.select();

			if (error) {
				console.error(`Error updating setting ${setting.key}:`, error);
				process.exit(1);
			}

			console.log(`✅ Setting ${setting.key} updated successfully`);
		}

		console.log("All email notification settings updated successfully!");
		process.exit(0);
	} catch (error) {
		console.error("Script error:", error);
		process.exit(1);
	}
}

updateEmailNotificationSettings();
