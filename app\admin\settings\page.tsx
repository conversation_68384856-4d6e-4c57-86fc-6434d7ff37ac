"use client";

import AdminLayout from "@/components/admin/AdminLayout";
import { useSettings } from "@/lib/use-settings";
import { Info, Loader2, Save } from "lucide-react";
import { useState } from "react";

export const dynamic = "force-dynamic";

export default function AdminSettingsPage() {
	const { categorized, loading, error, updateSetting } = useSettings();
	const [saving, setSaving] = useState<string | null>(null);
	const [localValues, setLocalValues] = useState<Record<string, any>>({});

	const handleValueChange = (key: string, value: any) => {
		setLocalValues((prev) => ({ ...prev, [key]: value }));
	};

	const handleSave = async (key: string, value: any) => {
		setSaving(key);
		try {
			const success = await updateSetting(key, value);
			if (success) {
				// Remove from local values since it's now saved
				setLocalValues((prev) => {
					const newValues = { ...prev };
					delete newValues[key];
					return newValues;
				});
			}
		} finally {
			setSaving(null);
		}
	};

	const getValue = (key: string, originalValue: any) => {
		if (localValues.hasOwnProperty(key)) {
			return localValues[key];
		}
		// For boolean values stored as strings in the database, convert them to actual booleans
		if (typeof originalValue === "string" && (originalValue === "true" || originalValue === "false")) {
			return originalValue === "true";
		}
		return originalValue;
	};

	const hasChanges = (key: string) => {
		return localValues.hasOwnProperty(key);
	};

	if (loading) {
		return (
			<AdminLayout adminOnly>
				<div className="flex items-center justify-center h-64">
					<div className="flex items-center space-x-2">
						<Loader2 className="h-6 w-6 animate-spin text-blue-600" />
						<span className="text-gray-600">Chargement des paramètres...</span>
					</div>
				</div>
			</AdminLayout>
		);
	}

	if (error) {
		return (
			<AdminLayout adminOnly>
				<div className="bg-red-50 border border-red-200 rounded-md p-4">
					<div className="flex">
						<Info className="h-5 w-5 text-red-400" />
						<div className="ml-3">
							<h3 className="text-sm font-medium text-red-800">Erreur</h3>
							<div className="mt-2 text-sm text-red-700">{error}</div>
						</div>
					</div>
				</div>
			</AdminLayout>
		);
	}

	const getCategoryTitle = (category: string) => {
		const titles: Record<string, string> = {
			business: "Informations de l'entreprise",
			business_info: "Informations de l'entreprise",
			booking: "Réservations",
			payment: "Paiements",
			notifications: "Notifications",
			notification: "Notifications",
			system: "Système",
			security: "Sécurité",
			general: "Général",
			schedule: "Horaires",
			social: "Réseaux sociaux",
		};
		return titles[category] || category.replace("_", " ");
	};

	// Function to get French description for settings
	const getSettingDescription = (key: string, originalDescription?: string) => {
		const frenchDescriptions: Record<string, string> = {
			email_notifications_enabled:
				"Contrôle principal pour toutes les notifications par email. Si désactivé, aucun email ne sera envoyé.",
			booking_confirmation_email_enabled: "Envoyer un email de confirmation lors des réservations",
			booking_confirmation_email: "Envoyer un email de confirmation lors des réservations",
			admin_email_notifications_enabled:
				"Recevoir des notifications par email pour les nouvelles réservations et paiements",
			reminder_email_hours: "Nombre d'heures avant le service pour envoyer un rappel par email",
			sms_notifications_enabled: "Activer ou désactiver les notifications par SMS",
			admin_notification_email: "Adresse email pour recevoir les notifications administratives",
		};

		return frenchDescriptions[key] || originalDescription || "";
	};

	// Function to determine if a setting is dependent on email notifications
	const isDependentOnEmailNotifications = (key: string) => {
		return [
			"booking_confirmation_email_enabled",
			"booking_confirmation_email", // Legacy key name
			"admin_email_notifications_enabled",
			"reminder_email_hours",
		].includes(key);
	};

	// Function to check if email notifications are enabled
	const areEmailNotificationsEnabled = () => {
		const emailNotificationsKey = "email_notifications_enabled";
		const emailNotificationsSetting = Object.values(categorized).find(
			(category) => category[emailNotificationsKey]
		)?.[emailNotificationsKey];

		if (emailNotificationsSetting) {
			return getValue(emailNotificationsKey, emailNotificationsSetting.value);
		}
		return true; // Default to enabled if not found
	};

	const getSettingLabel = (key: string) => {
		const labels: Record<string, string> = {
			// Informations de l'entreprise
			business_name: "Nom de l'entreprise",
			business_email: "Email de contact",
			business_phone: "Téléphone",
			business_address: "Adresse",
			business_description: "Description de l'entreprise",
			business_website: "Site web",
			business_logo: "Logo de l'entreprise",

			// Réservations
			booking_advance_days: "Réservation à l'avance (jours)",
			booking_cancellation_hours: "Annulation (heures à l'avance)",
			max_advance_booking: "Réservation maximum à l'avance",
			min_advance_booking: "Réservation minimum à l'avance",
			booking_confirmation_required: "Confirmation de réservation requise",
			booking_auto_confirm: "Confirmation automatique des réservations",
			booking_deposit_required: "Acompte requis pour les réservations",
			booking_deposit_percentage: "Pourcentage d'acompte",

			// Paiements
			payment_currency: "Devise",
			payment_methods: "Méthodes de paiement acceptées",
			payment_terms: "Conditions de paiement",
			stripe_enabled: "Paiements Stripe activés",
			cash_enabled: "Paiements en espèces activés",
			card_enabled: "Paiements par carte activés",

			// Notifications
			notification_email: "Notifications par email",
			notification_sms: "Notifications par SMS",
			admin_notification_email: "Email des notifications admin",
			email_notifications_enabled: "Notifications par email activées",
			booking_confirmation_email_enabled: "Email de confirmation de réservation",
			booking_confirmation_email: "Email de confirmation de réservation",
			admin_email_notifications_enabled: "Notifications admin par email",
			reminder_email_hours: "Heures avant rappel par email",
			sms_notifications_enabled: "Notifications SMS activées",
			email_from_name: "Nom d'expéditeur des emails",
			email_reply_to: "Email de réponse",

			// Horaires et jours
			operating_days: "Jours d'ouverture",
			business_hours: "Horaires d'ouverture",
			opening_time: "Heure d'ouverture",
			closing_time: "Heure de fermeture",
			lunch_break_start: "Début de pause déjeuner",
			lunch_break_end: "Fin de pause déjeuner",

			// Système et sécurité
			timezone: "Fuseau horaire",
			language: "Langue",
			date_format: "Format de date",
			time_format: "Format d'heure",
			currency_symbol: "Symbole de devise",
			tax_rate: "Taux de TVA",
			terms_and_conditions: "Conditions générales",
			privacy_policy: "Politique de confidentialité",

			// Capacités et limites
			max_participants_per_booking: "Participants maximum par réservation",
			min_participants_per_booking: "Participants minimum par réservation",
			default_service_duration: "Durée par défaut des services",
			booking_buffer_time: "Temps de battement entre réservations",

			// Réseaux sociaux
			facebook_url: "URL Facebook",
			instagram_url: "URL Instagram",
			twitter_url: "URL Twitter",
			youtube_url: "URL YouTube",

			// Autres
			google_analytics_id: "ID Google Analytics",
			google_maps_api_key: "Clé API Google Maps",
			weather_api_key: "Clé API météo",
			maintenance_mode: "Mode maintenance",
			debug_mode: "Mode débogage",
			facebook_url: "URL Facebook",
			instagram_url: "URL Instagram",
			tiktok_url: "URL TikTok",
			tripadvisor_url: "URL TripAdvisor",
		};
		return labels[key] || key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
	};

	return (
		<AdminLayout adminOnly>
			<div className="p-6">
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Paramètres</h1>
					<p className="text-gray-600">Configurez les paramètres de votre entreprise</p>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{Object.entries(categorized).map(([category, settings]) => (
						<div key={category} className="bg-white rounded-xl shadow-sm border border-gray-200">
							<div className="p-6 border-b border-gray-200">
								<h2 className="text-xl font-semibold text-gray-900">{getCategoryTitle(category)}</h2>
							</div>
							<div className="p-6 space-y-6">
								{/* Special handling for notifications category to show hierarchy */}
								{category === "notifications" || category === "notification" ? (
									<div className="space-y-4">
										{/* Master toggle first */}
										{Object.entries(settings)
											.filter(([key]) => key === "email_notifications_enabled")
											.map(([key, setting]) => (
												<div
													key={key}
													className="p-4 bg-blue-50 border-2 border-blue-200 rounded-lg"
												>
													<div className="flex items-center justify-between">
														<div className="flex-1">
															<label className="block text-sm font-bold text-blue-900">
																🔧 {getSettingLabel(key)} (Contrôle principal)
															</label>
															<p className="text-xs text-blue-700 mt-1 font-medium">
																{getSettingDescription(key, setting.description)}
															</p>
														</div>
														{hasChanges(key) && (
															<button
																onClick={() => handleSave(key, localValues[key])}
																disabled={saving === key}
																className="ml-3 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
															>
																{saving === key ? (
																	<Loader2 className="h-3 w-3 animate-spin" />
																) : (
																	<Save className="h-3 w-3" />
																)}
																<span className="ml-1">Sauvegarder</span>
															</button>
														)}
													</div>
													<div className="mt-3">
														<select
															value={getValue(key, setting.value).toString()}
															onChange={(e) =>
																handleValueChange(key, e.target.value === "true")
															}
															className="block w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm font-medium"
														>
															<option value="true">
																✅ Activé - Tous les emails peuvent être envoyés
															</option>
															<option value="false">
																❌ Désactivé - AUCUN email ne sera envoyé
															</option>
														</select>
													</div>
												</div>
											))}

										{/* Dependent settings */}
										<div
											className={`space-y-4 transition-opacity duration-200 ${!areEmailNotificationsEnabled() ? "opacity-50" : ""}`}
										>
											{Object.entries(settings)
												.filter(([key]) => isDependentOnEmailNotifications(key))
												.map(([key, setting]) => (
													<div
														key={key}
														className={`pl-6 border-l-4 ${areEmailNotificationsEnabled() ? "border-emerald-300 bg-emerald-50" : "border-gray-300 bg-gray-50"} p-4 rounded-r-lg`}
													>
														<div className="flex items-center justify-between">
															<div className="flex-1">
																<label
																	className={`block text-sm font-medium ${areEmailNotificationsEnabled() ? "text-emerald-800" : "text-gray-600"}`}
																>
																	↳ {getSettingLabel(key)}
																	{!areEmailNotificationsEnabled() && (
																		<span className="text-red-600 ml-2">
																			(Désactivé par le contrôle principal)
																		</span>
																	)}
																</label>
																<p
																	className={`text-xs mt-1 ${areEmailNotificationsEnabled() ? "text-emerald-700" : "text-gray-500"}`}
																>
																	{getSettingDescription(key, setting.description)}
																</p>
															</div>
															{hasChanges(key) && areEmailNotificationsEnabled() && (
																<button
																	onClick={() => handleSave(key, localValues[key])}
																	disabled={saving === key}
																	className="ml-3 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50"
																>
																	{saving === key ? (
																		<Loader2 className="h-3 w-3 animate-spin" />
																	) : (
																		<Save className="h-3 w-3" />
																	)}
																	<span className="ml-1">Sauvegarder</span>
																</button>
															)}
														</div>
														<div className="mt-3">
															{setting.type === "boolean" ? (
																<select
																	value={getValue(key, setting.value).toString()}
																	onChange={(e) =>
																		handleValueChange(
																			key,
																			e.target.value === "true"
																		)
																	}
																	disabled={!areEmailNotificationsEnabled()}
																	className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 text-sm ${
																		areEmailNotificationsEnabled()
																			? "border-emerald-300 focus:ring-emerald-500 focus:border-transparent"
																			: "border-gray-300 bg-gray-100 text-gray-500 cursor-not-allowed"
																	}`}
																>
																	<option value="true">Activé</option>
																	<option value="false">Désactivé</option>
																</select>
															) : (
																<input
																	type={
																		key.includes("email")
																			? "email"
																			: setting.type === "number"
																				? "number"
																				: "text"
																	}
																	value={getValue(key, setting.value)}
																	onChange={(e) =>
																		handleValueChange(
																			key,
																			setting.type === "number"
																				? parseFloat(e.target.value) || 0
																				: e.target.value
																		)
																	}
																	disabled={!areEmailNotificationsEnabled()}
																	className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 text-sm ${
																		areEmailNotificationsEnabled()
																			? "border-emerald-300 focus:ring-emerald-500 focus:border-transparent"
																			: "border-gray-300 bg-gray-100 text-gray-500 cursor-not-allowed"
																	}`}
																/>
															)}
														</div>
													</div>
												))}
										</div>

										{/* Other notification settings */}
										{Object.entries(settings)
											.filter(
												([key]) =>
													key !== "email_notifications_enabled" &&
													!isDependentOnEmailNotifications(key)
											)
											.map(([key, setting]) => (
												<div key={key} className="space-y-2">
													<div className="flex items-center justify-between">
														<div className="flex-1">
															<label className="block text-sm font-medium text-gray-700">
																{getSettingLabel(key)}
															</label>
															<p className="text-xs text-gray-500 mt-1">
																{getSettingDescription(key, setting.description)}
															</p>
														</div>
														{hasChanges(key) && (
															<button
																onClick={() => handleSave(key, localValues[key])}
																disabled={saving === key}
																className="ml-3 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50"
															>
																{saving === key ? (
																	<Loader2 className="h-3 w-3 animate-spin" />
																) : (
																	<Save className="h-3 w-3" />
																)}
																<span className="ml-1">Sauvegarder</span>
															</button>
														)}
													</div>
													<div className="mt-2">
														{setting.type === "boolean" ? (
															<select
																value={getValue(key, setting.value).toString()}
																onChange={(e) =>
																	handleValueChange(key, e.target.value === "true")
																}
																className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
															>
																<option value="true">Activé</option>
																<option value="false">Désactivé</option>
															</select>
														) : key.includes("email") ? (
															<input
																type="email"
																value={getValue(key, setting.value)}
																onChange={(e) => handleValueChange(key, e.target.value)}
																className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
																placeholder="<EMAIL>"
															/>
														) : setting.type === "number" ? (
															<input
																type="number"
																value={getValue(key, setting.value)}
																onChange={(e) =>
																	handleValueChange(
																		key,
																		parseFloat(e.target.value) || 0
																	)
																}
																className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
															/>
														) : (
															<input
																type="text"
																value={getValue(key, setting.value)}
																onChange={(e) => handleValueChange(key, e.target.value)}
																className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
															/>
														)}
													</div>
												</div>
											))}
									</div>
								) : (
									/* Standard rendering for non-notification categories */
									Object.entries(settings).map(([key, setting]) => (
										<div key={key} className="space-y-2">
											<div className="flex items-center justify-between">
												<div className="flex-1">
													<label className="block text-sm font-medium text-gray-700">
														{getSettingLabel(key)}
													</label>
													<p className="text-xs text-gray-500 mt-1">
														{getSettingDescription(key, setting.description)}
													</p>
												</div>
												{hasChanges(key) && (
													<button
														onClick={() => handleSave(key, localValues[key])}
														disabled={saving === key}
														className="ml-3 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50"
													>
														{saving === key ? (
															<Loader2 className="h-3 w-3 animate-spin" />
														) : (
															<Save className="h-3 w-3" />
														)}
														<span className="ml-1">Sauvegarder</span>
													</button>
												)}
											</div>

											<div className="mt-2">
												{setting.type === "boolean" ? (
													<select
														value={getValue(key, setting.value).toString()}
														onChange={(e) =>
															handleValueChange(key, e.target.value === "true")
														}
														className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
													>
														<option value="true">Activé</option>
														<option value="false">Désactivé</option>
													</select>
												) : key === "operating_days" ? (
													<div className="space-y-2">
														{[
															{ key: "monday", label: "Lundi" },
															{ key: "tuesday", label: "Mardi" },
															{ key: "wednesday", label: "Mercredi" },
															{ key: "thursday", label: "Jeudi" },
															{ key: "friday", label: "Vendredi" },
															{ key: "saturday", label: "Samedi" },
															{ key: "sunday", label: "Dimanche" },
														].map((day) => {
															const currentValue = getValue(key, setting.value) || [];
															const isChecked = currentValue.includes(day.key);
															return (
																<label key={day.key} className="flex items-center">
																	<input
																		type="checkbox"
																		checked={isChecked}
																		onChange={(e) => {
																			const newValue = e.target.checked
																				? [...currentValue, day.key]
																				: currentValue.filter(
																						(d: string) => d !== day.key
																					);
																			handleValueChange(key, newValue);
																		}}
																		className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
																	/>
																	<span className="ml-2 text-sm text-gray-700">
																		{day.label}
																	</span>
																</label>
															);
														})}
													</div>
												) : key === "payment_methods" ? (
													<div className="space-y-2">
														{[
															{ key: "cash", label: "Espèces" },
															{ key: "card", label: "Carte bancaire (TPE)" },
															{ key: "bank_transfer", label: "Virement bancaire" },
															{ key: "check", label: "Chèque" },
															{ key: "stripe", label: "Stripe" },
															{ key: "paypal", label: "PayPal" },
														].map((method) => {
															const currentValue = getValue(key, setting.value) || [];
															const isChecked = currentValue.includes(method.key);
															return (
																<label key={method.key} className="flex items-center">
																	<input
																		type="checkbox"
																		checked={isChecked}
																		onChange={(e) => {
																			const newValue = e.target.checked
																				? [...currentValue, method.key]
																				: currentValue.filter(
																						(m: string) => m !== method.key
																					);
																			handleValueChange(key, newValue);
																		}}
																		className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
																	/>
																	<span className="ml-2 text-sm text-gray-700">
																		{method.label}
																	</span>
																</label>
															);
														})}
													</div>
												) : setting.type === "json" ? (
													<textarea
														value={JSON.stringify(getValue(key, setting.value), null, 2)}
														onChange={(e) => {
															try {
																const parsed = JSON.parse(e.target.value);
																handleValueChange(key, parsed);
															} catch {
																handleValueChange(key, e.target.value);
															}
														}}
														rows={3}
														className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm font-mono"
													/>
												) : setting.type === "number" ? (
													<input
														type="number"
														value={getValue(key, setting.value)}
														onChange={(e) =>
															handleValueChange(key, parseFloat(e.target.value) || 0)
														}
														className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
													/>
												) : key.includes("email") ? (
													<input
														type="email"
														value={getValue(key, setting.value)}
														onChange={(e) => handleValueChange(key, e.target.value)}
														className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
														placeholder="<EMAIL>"
													/>
												) : (
													<input
														type="text"
														value={getValue(key, setting.value)}
														onChange={(e) => handleValueChange(key, e.target.value)}
														className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
													/>
												)}
											</div>
										</div>
									))
								)}
							</div>
						</div>
					))}
				</div>
			</div>
		</AdminLayout>
	);
}
